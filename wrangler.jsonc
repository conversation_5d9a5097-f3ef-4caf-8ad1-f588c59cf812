/**
 * For more details on how to configure Wrangler, refer to:
 * https://developers.cloudflare.com/workers/wrangler/configuration/
 */
{
	"$schema": "node_modules/wrangler/config-schema.json",
	"name": "remote-mcp-server-authless",
	"main": "src/index.ts",
	"compatibility_date": "2025-03-10",
	"compatibility_flags": ["nodejs_compat"],

	"durable_objects": {
		"bindings": [
			{
				"class_name": "MyMCP",
				"name": "MCP_OBJECT"
			}
		]
	},
	"d1_databases": [
		{
			"binding": "DB",
			"database_name": "interactive-feedback-db",
			"database_id": "4ca1a6f7-a35e-4585-a7da-07f59528470b"
		}
	],
	"kv_namespaces": [
		{
			"binding": "OAUTH_KV",
			"id": "78857d9441204fbebd5b9db9d11b6909",
			"preview_id": "preview_id"
		}
	],
	"observability": {
		"enabled": true
	},
	// 环境变量配置示例 - 在生产环境中请使用 wrangler secret 命令设置敏感信息
	"vars": {
		"JWT_SECRET": "ibtZyMQ0_OOtm5BUIYVKa9o0Qy_Kx3N_NC0vqL-Eev4"
	}
}
