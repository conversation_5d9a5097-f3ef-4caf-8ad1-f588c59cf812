<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Interactive Feedback Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .iframe-container {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            margin: 20px 0;
        }
        iframe {
            width: 100%;
            height: 700px;
            border: none;
        }
        .code {
            background: #f1f3f4;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: #e3f2fd;
            padding: 16px;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
        }
        .feature-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <h1>🚀 Enhanced Interactive Feedback System Test</h1>
    <p>测试改进后的Interactive Feedback系统，包括反馈列表、AI内容渲染和增强的用户体验。</p>

    <div class="test-section">
        <h2>🎯 新功能特性</h2>
        <div class="feature-grid">
            <div class="feature-card">
                <div class="feature-title">📋 反馈列表页面</div>
                <p>统一的入口页面，显示所有待处理的反馈任务，支持状态过滤和实时刷新。</p>
            </div>
            <div class="feature-card">
                <div class="feature-title">🤖 AI内容渲染</div>
                <p>支持Markdown格式的AI反馈内容，包括粗体、斜体、代码块等格式。</p>
            </div>
            <div class="feature-card">
                <div class="feature-title">🎨 增强的UI/UX</div>
                <p>现代化的界面设计，响应式布局，主题切换，多语言支持。</p>
            </div>
            <div class="feature-card">
                <div class="feature-title">🔄 完整的工作流程</div>
                <p>从列表查看 → 点击任务 → 查看AI内容 → 提供反馈 → 提交完成。</p>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🧪 API测试</h2>
        <button class="test-button" onclick="createAIFeedbackSession()">创建AI反馈会话</button>
        <button class="test-button" onclick="createRegularSession()">创建普通反馈会话</button>
        <button class="test-button" onclick="getFeedbackList()">获取反馈列表</button>
        <button class="test-button" onclick="testSubmitFeedback()">测试提交反馈</button>
        <div id="api-results"></div>
    </div>

    <div class="test-section">
        <h2>🌐 Web界面测试</h2>
        <p>测试不同的页面和主题：</p>
        <button class="test-button" onclick="loadFeedbackList('dark', 'zh')">反馈列表 (暗色中文)</button>
        <button class="test-button" onclick="loadFeedbackList('light', 'en')">Feedback List (Light English)</button>
        <button class="test-button" onclick="loadFeedbackDetail()">反馈详情页面</button>
        <button class="test-button" onclick="loadAIContentPage()">AI内容页面</button>
        
        <div class="iframe-container">
            <iframe id="test-iframe" src="about:blank"></iframe>
        </div>
    </div>

    <div class="test-section">
        <h2>📊 测试结果</h2>
        <div id="test-summary">
            <p>点击上面的按钮开始测试...</p>
        </div>
    </div>

    <script>
        const BASE_URL = 'http://127.0.0.1:8787';
        const API_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiJ0ZXN0LXVzZXIiLCJ1c2VybmFtZSI6InRlc3R1c2VyIiwicm9sZXMiOlsidXNlciJdLCJpYXQiOjE3NTAwNjczMjcsImV4cCI6MTc1MDA3MDkyN30.5yNkc52U5_KDTbTljzJsd_I5RdxrztgDxC22_PJX6i4';
        
        let currentSessionId = null;
        let aiSessionId = null;

        function showResult(containerId, message, isSuccess = true) {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.innerHTML = message;
            container.appendChild(resultDiv);
        }

        async function createAIFeedbackSession() {
            try {
                showResult('api-results', '🔄 正在创建包含AI内容的反馈会话...', true);
                
                const response = await fetch(`${BASE_URL}/api/feedback/create`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        title: "代码审查反馈",
                        message: "请对以下代码实现进行评价和建议",
                        aiContent: "## 代码分析结果\\n\\n经过分析，您的代码实现有以下**优点**：\\n\\n1. **结构清晰**：模块化设计良好\\n2. **类型安全**：使用了TypeScript类型定义\\n3. **错误处理**：包含了完善的异常处理机制\\n\\n### 建议改进\\n\\n- 可以考虑添加更多的*单元测试*\\n- 建议使用`async/await`替代Promise链\\n- 性能优化：考虑使用缓存机制\\n\\n**总体评分：8.5/10**",
                        predefinedOptions: ["代码结构优秀", "类型定义完善", "错误处理到位", "需要更多测试", "性能可以优化"],
                        timeout: 1800
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    aiSessionId = result.data.sessionId;
                    showResult('api-results', `
                        ✅ <strong>AI反馈会话创建成功！</strong><br>
                        Session ID: <code>${result.data.sessionId}</code><br>
                        包含AI内容的反馈页面: <a href="${result.data.feedbackUrl}" target="_blank">打开页面</a>
                    `, true);
                } else {
                    showResult('api-results', `❌ 创建失败: ${result.error.message}`, false);
                }
            } catch (error) {
                showResult('api-results', `❌ 请求失败: ${error.message}`, false);
            }
        }

        async function createRegularSession() {
            try {
                showResult('api-results', '🔄 正在创建普通反馈会话...', true);
                
                const response = await fetch(`${BASE_URL}/api/feedback/create`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        title: "用户体验调研",
                        message: "请对我们的新功能进行评价，您的反馈对我们很重要。",
                        predefinedOptions: ["界面美观", "功能实用", "操作简单", "响应快速", "需要改进"],
                        timeout: 1200
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    currentSessionId = result.data.sessionId;
                    showResult('api-results', `
                        ✅ <strong>普通反馈会话创建成功！</strong><br>
                        Session ID: <code>${result.data.sessionId}</code><br>
                        反馈页面: <a href="${result.data.feedbackUrl}" target="_blank">打开页面</a>
                    `, true);
                } else {
                    showResult('api-results', `❌ 创建失败: ${result.error.message}`, false);
                }
            } catch (error) {
                showResult('api-results', `❌ 请求失败: ${error.message}`, false);
            }
        }

        async function getFeedbackList() {
            try {
                showResult('api-results', '🔄 正在获取反馈列表...', true);
                
                const response = await fetch(`${BASE_URL}/api/feedback/list?status=pending`, {
                    headers: {
                        'Authorization': `Bearer ${API_KEY}`
                    }
                });

                const result = await response.json();
                
                if (result.success) {
                    showResult('api-results', `
                        ✅ <strong>反馈列表获取成功！</strong><br>
                        总任务数: ${result.data.total}<br>
                        待处理: ${result.data.pending}<br>
                        已完成: ${result.data.completed}<br>
                        当前显示: ${result.data.items.length} 个待处理任务
                    `, true);
                } else {
                    showResult('api-results', `❌ 获取失败: ${result.error.message}`, false);
                }
            } catch (error) {
                showResult('api-results', `❌ 请求失败: ${error.message}`, false);
            }
        }

        async function testSubmitFeedback() {
            const sessionId = currentSessionId || aiSessionId;
            if (!sessionId) {
                showResult('api-results', '❌ 请先创建一个反馈会话', false);
                return;
            }

            try {
                showResult('api-results', '🔄 正在提交测试反馈...', true);
                
                const response = await fetch(`${BASE_URL}/api/feedback/${sessionId}/submit`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        selectedOptions: ["界面美观", "功能实用"],
                        freeText: "整体体验很好，特别是新的反馈列表功能很实用！AI内容渲染也很棒。",
                        metadata: {
                            testMode: true,
                            userAgent: navigator.userAgent,
                            timestamp: new Date().toISOString()
                        }
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    showResult('api-results', `
                        ✅ <strong>反馈提交成功！</strong><br>
                        Session ID: ${result.data.sessionId}<br>
                        状态: ${result.data.status}<br>
                        提交时间: ${new Date(result.data.submittedAt).toLocaleString()}
                    `, true);
                } else {
                    showResult('api-results', `❌ 提交失败: ${result.error.message}`, false);
                }
            } catch (error) {
                showResult('api-results', `❌ 请求失败: ${error.message}`, false);
            }
        }

        function loadFeedbackList(theme, lang) {
            const iframe = document.getElementById('test-iframe');
            const url = `${BASE_URL}/feedback?theme=${theme}&lang=${lang}`;
            iframe.src = url;
            
            showResult('api-results', `
                🎨 <strong>加载反馈列表页面</strong><br>
                主题: ${theme}<br>
                语言: ${lang}<br>
                URL: <a href="${url}" target="_blank">${url}</a>
            `, true);
        }

        function loadFeedbackDetail() {
            const sessionId = currentSessionId || aiSessionId;
            if (!sessionId) {
                alert('请先创建一个反馈会话');
                return;
            }

            const iframe = document.getElementById('test-iframe');
            const url = `${BASE_URL}/feedback/${sessionId}?theme=dark&lang=zh`;
            iframe.src = url;
            
            showResult('api-results', `
                📝 <strong>加载反馈详情页面</strong><br>
                Session ID: ${sessionId}<br>
                URL: <a href="${url}" target="_blank">${url}</a>
            `, true);
        }

        function loadAIContentPage() {
            if (!aiSessionId) {
                alert('请先创建一个包含AI内容的反馈会话');
                return;
            }

            const iframe = document.getElementById('test-iframe');
            const url = `${BASE_URL}/feedback/${aiSessionId}?theme=light&lang=en`;
            iframe.src = url;
            
            showResult('api-results', `
                🤖 <strong>加载AI内容页面</strong><br>
                Session ID: ${aiSessionId}<br>
                主题: Light English<br>
                URL: <a href="${url}" target="_blank">${url}</a>
            `, true);
        }

        // 页面加载时的初始化
        window.onload = function() {
            showResult('api-results', '🚀 Enhanced Interactive Feedback测试工具已准备就绪。开始测试新功能！', true);
        };
    </script>
</body>
</html>
