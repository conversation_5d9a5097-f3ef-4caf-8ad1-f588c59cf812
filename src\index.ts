import { McpAgent } from "agents/mcp";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from "zod";
import { authenticate, createAuthErrorResponse } from "./auth.js";
import { CreateFeedbackRequest, CreateFeedbackResponse } from "./types/feedback.js";
import { FeedbackService } from "./services/feedbackService.js";

// Define our MCP agent with Interactive Feedback tools
export class MyMCP extends McpAgent {
	server = new McpServer({
		name: "Interactive Feedback MCP",
		version: "1.0.0",
	});

	async init() {
		// Interactive feedback tool
		this.server.tool(
			"interactive_feedback",
			{
				message: z.string().min(1).max(1000),
				predefinedOptions: z.array(z.string().max(100)).max(10).optional(),
				timeout: z.number().min(30).max(3600).optional(),
			},
			async ({ message, predefinedOptions, timeout }) => {
				try {
					// 创建反馈会话
					const sessionId = crypto.randomUUID();
					const expiresAt = new Date(Date.now() + (timeout || 300) * 1000);

					const session = {
						sessionId,
						message,
						predefinedOptions,
						status: 'pending' as const,
						createdAt: new Date().toISOString(),
						expiresAt: expiresAt.toISOString(),
					};

					// 存储到KV（这里先用简单实现，后续会用FeedbackService）
					const kvKey = `feedback:session:${sessionId}`;
					await this.env.OAUTH_KV.put(kvKey, JSON.stringify(session), {
						expirationTtl: timeout || 300
					});

					const baseUrl = this.getBaseUrl();
					const feedbackUrl = `${baseUrl}/feedback/${sessionId}`;
					const statusUrl = `${baseUrl}/api/feedback/${sessionId}/status`;

					return {
						content: [
							{
								type: "text",
								text: `Interactive feedback session created successfully!\n\n` +
									  `Session ID: ${sessionId}\n` +
									  `Feedback URL: ${feedbackUrl}\n` +
									  `Status URL: ${statusUrl}\n` +
									  `Expires at: ${expiresAt.toISOString()}\n\n` +
									  `Please share the feedback URL with the user to collect their response.`
							}
						]
					};
				} catch (error) {
					return {
						content: [
							{
								type: "text",
								text: `Error creating feedback session: ${error instanceof Error ? error.message : 'Unknown error'}`
							}
						]
					};
				}
			}
		);
	}

	private getBaseUrl(): string {
		// 在实际环境中，这应该从请求中获取
		// 这里先用一个默认值，后续会改进
		return 'https://remote-mcp-server-authless.sujianjob.workers.dev';
	}

	// 添加env属性以便访问KV
	env!: Env;
}

export default {
	async fetch(request: Request, env: Env, ctx: ExecutionContext) {
		const url = new URL(request.url);

		// 健康检查端点（无需鉴权）
		if (url.pathname === "/health") {
			return new Response(JSON.stringify({
				status: "ok",
				timestamp: new Date().toISOString(),
				service: "MCP Server with JWT Auth"
			}), {
				headers: {
					"Content-Type": "application/json"
				}
			});
		}

		// 对MCP端点进行鉴权
		if (url.pathname === "/sse" || url.pathname === "/sse/message" || url.pathname === "/mcp") {
			const authResult = await authenticate(request, env);

			if (!authResult.success) {
				console.log(`Authentication failed: ${authResult.error}`);
				return createAuthErrorResponse(authResult.error || "Authentication failed");
			}

			console.log(`Authentication successful for user: ${authResult.userId}`);

			// 在请求头中添加用户信息，供下游使用
			const modifiedRequest = new Request(request, {
				headers: {
					...Object.fromEntries(request.headers.entries()),
					'X-User-ID': authResult.userId || 'unknown'
				}
			});

			if (url.pathname === "/sse" || url.pathname === "/sse/message") {
				return MyMCP.serveSSE("/sse").fetch(modifiedRequest, env, ctx);
			}

			if (url.pathname === "/mcp") {
				return MyMCP.serve("/mcp").fetch(modifiedRequest, env, ctx);
			}
		}

		return new Response("Not found", { status: 404 });
	},
};
