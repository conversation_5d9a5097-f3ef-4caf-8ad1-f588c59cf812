/**
 * Interactive Feedback MCP 系统类型定义
 */

// 会话状态枚举
export type SessionStatus = 'pending' | 'completed' | 'expired';

// 会话数据结构
export interface FeedbackSession {
	sessionId: string;
	message: string;
	predefinedOptions?: string[];
	status: SessionStatus;
	createdAt: string;
	expiresAt: string;
	feedback?: FeedbackData;
	userAgent?: string;
	ipAddress?: string;
}

// 反馈数据结构
export interface FeedbackData {
	type: 'predefined' | 'text';
	value: string;
	submittedAt: string;
	userAgent?: string;
}

// API 请求类型
export interface CreateFeedbackRequest {
	message: string;
	predefinedOptions?: string[];
	timeout?: number; // 超时时间（秒），默认300秒
}

// API 响应类型
export interface CreateFeedbackResponse {
	sessionId: string;
	feedbackUrl: string;
	statusUrl: string;
	expiresAt: string;
}

export interface FeedbackStatusResponse {
	sessionId: string;
	status: SessionStatus;
	message: string;
	predefinedOptions?: string[];
	feedback?: FeedbackData;
	createdAt: string;
	expiresAt: string;
}

export interface SubmitFeedbackRequest {
	type: 'predefined' | 'text';
	value: string;
}

export interface SubmitFeedbackResponse {
	success: boolean;
	message: string;
	redirectUrl?: string;
}

// 错误响应类型
export interface ErrorResponse {
	error: string;
	code?: string;
	details?: any;
}

// 会话配置
export interface SessionConfig {
	defaultTimeout: number;
	maxTimeout: number;
	cleanupInterval: number;
	maxMessageLength: number;
	maxOptionsCount: number;
	maxOptionLength: number;
}

// KV 存储键格式
export const KV_KEYS = {
	session: (sessionId: string) => `feedback:session:${sessionId}`,
	cleanup: 'feedback:cleanup:last',
} as const;

// 常量定义
export const CONSTANTS = {
	DEFAULT_TIMEOUT: 300, // 5分钟
	MAX_TIMEOUT: 3600, // 1小时
	MAX_MESSAGE_LENGTH: 1000,
	MAX_OPTIONS_COUNT: 10,
	MAX_OPTION_LENGTH: 100,
	CLEANUP_INTERVAL: 3600000, // 1小时（毫秒）
} as const;
