/**
 * 反馈会话管理服务
 */

import {
	FeedbackSession,
	FeedbackData,
	CreateFeedbackRequest,
	CreateFeedbackResponse,
	FeedbackStatusResponse,
	SubmitFeedbackRequest,
	SubmitFeedbackResponse,
	SessionStatus,
	KV_KEYS,
	CONSTANTS
} from '../types/feedback.js';

export class FeedbackService {
	constructor(private kv: KVNamespace) {}

	/**
	 * 创建新的反馈会话
	 */
	async createSession(request: CreateFeedbackRequest, baseUrl: string): Promise<CreateFeedbackResponse> {
		// 验证输入
		this.validateCreateRequest(request);

		const sessionId = crypto.randomUUID();
		const timeout = Math.min(request.timeout || CONSTANTS.DEFAULT_TIMEOUT, CONSTANTS.MAX_TIMEOUT);
		const expiresAt = new Date(Date.now() + timeout * 1000);

		const session: FeedbackSession = {
			sessionId,
			message: request.message,
			predefinedOptions: request.predefinedOptions,
			status: 'pending',
			createdAt: new Date().toISOString(),
			expiresAt: expiresAt.toISOString(),
		};

		// 存储到KV
		const kvKey = KV_KEYS.session(sessionId);
		await this.kv.put(kvKey, JSON.stringify(session), {
			expirationTtl: timeout
		});

		// 返回响应
		return {
			sessionId,
			feedbackUrl: `${baseUrl}/feedback/${sessionId}`,
			statusUrl: `${baseUrl}/api/feedback/${sessionId}/status`,
			expiresAt: expiresAt.toISOString(),
		};
	}

	/**
	 * 获取会话状态
	 */
	async getSessionStatus(sessionId: string): Promise<FeedbackStatusResponse | null> {
		const kvKey = KV_KEYS.session(sessionId);
		const sessionData = await this.kv.get(kvKey);

		if (!sessionData) {
			return null;
		}

		try {
			const session: FeedbackSession = JSON.parse(sessionData);
			
			// 检查是否过期
			if (new Date() > new Date(session.expiresAt)) {
				session.status = 'expired';
				// 更新状态到KV
				await this.kv.put(kvKey, JSON.stringify(session), {
					expirationTtl: 60 // 过期会话保留1分钟用于查询
				});
			}

			return {
				sessionId: session.sessionId,
				status: session.status,
				message: session.message,
				predefinedOptions: session.predefinedOptions,
				feedback: session.feedback,
				createdAt: session.createdAt,
				expiresAt: session.expiresAt,
			};
		} catch (error) {
			console.error('Error parsing session data:', error);
			return null;
		}
	}

	/**
	 * 提交反馈
	 */
	async submitFeedback(sessionId: string, request: SubmitFeedbackRequest): Promise<SubmitFeedbackResponse> {
		const kvKey = KV_KEYS.session(sessionId);
		const sessionData = await this.kv.get(kvKey);

		if (!sessionData) {
			return {
				success: false,
				message: 'Session not found or expired'
			};
		}

		try {
			const session: FeedbackSession = JSON.parse(sessionData);

			// 检查会话状态
			if (session.status !== 'pending') {
				return {
					success: false,
					message: 'Session is not accepting feedback'
				};
			}

			// 检查是否过期
			if (new Date() > new Date(session.expiresAt)) {
				session.status = 'expired';
				await this.kv.put(kvKey, JSON.stringify(session), {
					expirationTtl: 60
				});
				return {
					success: false,
					message: 'Session has expired'
				};
			}

			// 验证反馈内容
			this.validateFeedbackRequest(request, session);

			// 更新会话状态
			const feedback: FeedbackData = {
				type: request.type,
				value: request.value,
				submittedAt: new Date().toISOString(),
			};

			session.feedback = feedback;
			session.status = 'completed';

			// 保存更新后的会话
			await this.kv.put(kvKey, JSON.stringify(session), {
				expirationTtl: 3600 // 完成的会话保留1小时
			});

			return {
				success: true,
				message: 'Feedback submitted successfully'
			};
		} catch (error) {
			console.error('Error submitting feedback:', error);
			return {
				success: false,
				message: 'Error processing feedback'
			};
		}
	}

	/**
	 * 清理过期会话
	 */
	async cleanupExpiredSessions(): Promise<void> {
		// 这是一个简化的清理实现
		// 在实际应用中，可能需要更复杂的清理策略
		const lastCleanup = await this.kv.get(KV_KEYS.cleanup);
		const now = Date.now();
		
		if (lastCleanup) {
			const lastCleanupTime = parseInt(lastCleanup);
			if (now - lastCleanupTime < CONSTANTS.CLEANUP_INTERVAL) {
				return; // 还没到清理时间
			}
		}

		// 更新清理时间戳
		await this.kv.put(KV_KEYS.cleanup, now.toString());
		
		// 注意：KV的自动过期机制会处理大部分清理工作
		// 这里主要是记录清理时间
		console.log('Cleanup task executed at:', new Date().toISOString());
	}

	/**
	 * 验证创建请求
	 */
	private validateCreateRequest(request: CreateFeedbackRequest): void {
		if (!request.message || request.message.length > CONSTANTS.MAX_MESSAGE_LENGTH) {
			throw new Error('Invalid message length');
		}

		if (request.predefinedOptions) {
			if (request.predefinedOptions.length > CONSTANTS.MAX_OPTIONS_COUNT) {
				throw new Error('Too many predefined options');
			}
			
			for (const option of request.predefinedOptions) {
				if (option.length > CONSTANTS.MAX_OPTION_LENGTH) {
					throw new Error('Predefined option too long');
				}
			}
		}

		if (request.timeout && (request.timeout < 30 || request.timeout > CONSTANTS.MAX_TIMEOUT)) {
			throw new Error('Invalid timeout value');
		}
	}

	/**
	 * 验证反馈请求
	 */
	private validateFeedbackRequest(request: SubmitFeedbackRequest, session: FeedbackSession): void {
		if (!request.value || request.value.trim().length === 0) {
			throw new Error('Feedback value cannot be empty');
		}

		if (request.type === 'predefined') {
			if (!session.predefinedOptions || !session.predefinedOptions.includes(request.value)) {
				throw new Error('Invalid predefined option');
			}
		} else if (request.type === 'text') {
			if (request.value.length > CONSTANTS.MAX_MESSAGE_LENGTH) {
				throw new Error('Feedback text too long');
			}
		} else {
			throw new Error('Invalid feedback type');
		}
	}
}
