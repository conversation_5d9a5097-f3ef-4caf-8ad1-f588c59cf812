/**
 * 反馈API处理器
 */

import { FeedbackService } from '../services/feedbackService.js';
import {
	CreateFeedbackRequest,
	SubmitFeedbackRequest,
	ErrorResponse
} from '../types/feedback.js';

export class FeedbackHandler {
	constructor(private feedbackService: FeedbackService) {}

	/**
	 * 处理创建反馈会话请求
	 */
	async handleCreateSession(request: Request, baseUrl: string): Promise<Response> {
		try {
			if (request.method !== 'POST') {
				return this.createErrorResponse('Method not allowed', 405);
			}

			const body = await request.json() as CreateFeedbackRequest;
			const response = await this.feedbackService.createSession(body, baseUrl);

			return new Response(JSON.stringify(response), {
				status: 201,
				headers: {
					'Content-Type': 'application/json',
				},
			});
		} catch (error) {
			console.error('Error creating feedback session:', error);
			return this.createErrorResponse(
				error instanceof Error ? error.message : 'Internal server error',
				500
			);
		}
	}

	/**
	 * 处理获取会话状态请求
	 */
	async handleGetStatus(sessionId: string): Promise<Response> {
		try {
			const status = await this.feedbackService.getSessionStatus(sessionId);

			if (!status) {
				return this.createErrorResponse('Session not found', 404);
			}

			return new Response(JSON.stringify(status), {
				status: 200,
				headers: {
					'Content-Type': 'application/json',
				},
			});
		} catch (error) {
			console.error('Error getting session status:', error);
			return this.createErrorResponse('Internal server error', 500);
		}
	}

	/**
	 * 处理提交反馈请求
	 */
	async handleSubmitFeedback(request: Request, sessionId: string): Promise<Response> {
		try {
			if (request.method !== 'POST') {
				return this.createErrorResponse('Method not allowed', 405);
			}

			const body = await request.json() as SubmitFeedbackRequest;
			const response = await this.feedbackService.submitFeedback(sessionId, body);

			const status = response.success ? 200 : 400;
			return new Response(JSON.stringify(response), {
				status,
				headers: {
					'Content-Type': 'application/json',
				},
			});
		} catch (error) {
			console.error('Error submitting feedback:', error);
			return this.createErrorResponse('Internal server error', 500);
		}
	}

	/**
	 * 创建错误响应
	 */
	private createErrorResponse(message: string, status: number): Response {
		const errorResponse: ErrorResponse = {
			error: message,
			code: status.toString(),
		};

		return new Response(JSON.stringify(errorResponse), {
			status,
			headers: {
				'Content-Type': 'application/json',
			},
		});
	}
}

/**
 * 解析URL路径参数
 */
export function parseSessionId(pathname: string): string | null {
	// 匹配 /api/feedback/{sessionId}/status 或 /api/feedback/{sessionId}/submit
	const match = pathname.match(/^\/api\/feedback\/([^\/]+)\/?(status|submit)?$/);
	return match ? match[1] : null;
}

/**
 * 获取基础URL
 */
export function getBaseUrl(request: Request): string {
	const url = new URL(request.url);
	return `${url.protocol}//${url.host}`;
}
